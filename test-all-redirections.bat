@echo off
setlocal enabledelayedexpansion

:: Set console to UTF-8
chcp 65001 >nul 2>&1

echo ========================================
echo COMPREHENSIVE REDIRECTION TESTING
echo ========================================

cd /d "%~dp0"

:: Create test directory
set "TEST_DIR=%~dp0redirection_tests"
set "LOG_FILE=%TEST_DIR%\test_results.log"
set "ERROR_FILE=%TEST_DIR%\test_errors.log"

if not exist "%TEST_DIR%" mkdir "%TEST_DIR%"

echo [%date% %time%] Starting comprehensive redirection tests... >"%LOG_FILE%" 2>&1
echo [%date% %time%] Error log initialized >"%ERROR_FILE%" 2>&1

echo Testing all redirection scenarios...
echo.

:: Test 1: Basic output redirection
echo [TEST 1] Basic output redirection...
set "test_file=%TEST_DIR%\test1.txt"
echo This is test 1 >"%test_file%" 2>>"%ERROR_FILE%"
if exist "%test_file%" (
    echo ✅ PASS: Basic output redirection
    echo [%date% %time%] PASS: Basic output redirection >>"%LOG_FILE%" 2>&1
    del "%test_file%"
) else (
    echo ❌ FAIL: Basic output redirection
    echo [%date% %time%] FAIL: Basic output redirection >>"%LOG_FILE%" 2>&1
)

:: Test 2: Append redirection
echo [TEST 2] Append redirection...
set "test_file=%TEST_DIR%\test2.txt"
echo Line 1 >"%test_file%" 2>>"%ERROR_FILE%"
echo Line 2 >>"%test_file%" 2>>"%ERROR_FILE%"
if exist "%test_file%" (
    for /f %%i in ('find /c /v "" ^< "%test_file%"') do set line_count=%%i
    if !line_count! EQU 2 (
        echo ✅ PASS: Append redirection
        echo [%date% %time%] PASS: Append redirection >>"%LOG_FILE%" 2>&1
    ) else (
        echo ❌ FAIL: Append redirection - Expected 2 lines, got !line_count!
        echo [%date% %time%] FAIL: Append redirection >>"%LOG_FILE%" 2>&1
    )
    del "%test_file%"
) else (
    echo ❌ FAIL: Append redirection - File not created
    echo [%date% %time%] FAIL: Append redirection - File not created >>"%LOG_FILE%" 2>&1
)

:: Test 3: Error redirection
echo [TEST 3] Error redirection...
set "test_file=%TEST_DIR%\test3.txt"
nonexistent_command_12345 2>"%test_file%" >nul
if exist "%test_file%" (
    echo ✅ PASS: Error redirection
    echo [%date% %time%] PASS: Error redirection >>"%LOG_FILE%" 2>&1
    del "%test_file%"
) else (
    echo ❌ FAIL: Error redirection
    echo [%date% %time%] FAIL: Error redirection >>"%LOG_FILE%" 2>&1
)

:: Test 4: Combined redirection (stdout and stderr)
echo [TEST 4] Combined redirection...
set "test_file=%TEST_DIR%\test4.txt"
echo Test output >"%test_file%" 2>&1
if exist "%test_file%" (
    echo ✅ PASS: Combined redirection
    echo [%date% %time%] PASS: Combined redirection >>"%LOG_FILE%" 2>&1
    del "%test_file%"
) else (
    echo ❌ FAIL: Combined redirection
    echo [%date% %time%] FAIL: Combined redirection >>"%LOG_FILE%" 2>&1
)

:: Test 5: Variable expansion in redirection
echo [TEST 5] Variable expansion in redirection...
set "test_var=test5.txt"
set "test_file=%TEST_DIR%\!test_var!"
echo Variable test >"%test_file%" 2>>"%ERROR_FILE%"
if exist "%test_file%" (
    echo ✅ PASS: Variable expansion in redirection
    echo [%date% %time%] PASS: Variable expansion >>"%LOG_FILE%" 2>&1
    del "%test_file%"
) else (
    echo ❌ FAIL: Variable expansion in redirection
    echo [%date% %time%] FAIL: Variable expansion >>"%LOG_FILE%" 2>&1
)

:: Test 6: PowerShell redirection
echo [TEST 6] PowerShell redirection...
set "test_file=%TEST_DIR%\test6.txt"
powershell -Command "Write-Output 'PowerShell test'" >"%test_file%" 2>>"%ERROR_FILE%"
if exist "%test_file%" (
    echo ✅ PASS: PowerShell redirection
    echo [%date% %time%] PASS: PowerShell redirection >>"%LOG_FILE%" 2>&1
    del "%test_file%"
) else (
    echo ❌ FAIL: PowerShell redirection
    echo [%date% %time%] FAIL: PowerShell redirection >>"%LOG_FILE%" 2>&1
)

:: Test 7: Long path redirection
echo [TEST 7] Long path redirection...
set "long_dir=%TEST_DIR%\very_long_directory_name_for_testing_redirection_with_spaces_and_special_chars"
set "test_file=%long_dir%\test7.txt"
if not exist "%long_dir%" mkdir "%long_dir%"
echo Long path test >"%test_file%" 2>>"%ERROR_FILE%"
if exist "%test_file%" (
    echo ✅ PASS: Long path redirection
    echo [%date% %time%] PASS: Long path redirection >>"%LOG_FILE%" 2>&1
    del "%test_file%"
    rmdir "%long_dir%"
) else (
    echo ❌ FAIL: Long path redirection
    echo [%date% %time%] FAIL: Long path redirection >>"%LOG_FILE%" 2>&1
    if exist "%long_dir%" rmdir "%long_dir%"
)

:: Test 8: Unicode/Special characters
echo [TEST 8] Unicode and special characters...
set "test_file=%TEST_DIR%\test8.txt"
echo Testing special chars: éñüñ àáâã >"%test_file%" 2>>"%ERROR_FILE%"
if exist "%test_file%" (
    echo ✅ PASS: Unicode redirection
    echo [%date% %time%] PASS: Unicode redirection >>"%LOG_FILE%" 2>&1
    del "%test_file%"
) else (
    echo ❌ FAIL: Unicode redirection
    echo [%date% %time%] FAIL: Unicode redirection >>"%LOG_FILE%" 2>&1
)

:: Test 9: Multiple redirections in sequence
echo [TEST 9] Multiple redirections in sequence...
set "test_file=%TEST_DIR%\test9.txt"
(
    echo Line 1
    echo Line 2
    echo Line 3
) >"%test_file%" 2>>"%ERROR_FILE%"
if exist "%test_file%" (
    echo ✅ PASS: Multiple redirections
    echo [%date% %time%] PASS: Multiple redirections >>"%LOG_FILE%" 2>&1
    del "%test_file%"
) else (
    echo ❌ FAIL: Multiple redirections
    echo [%date% %time%] FAIL: Multiple redirections >>"%LOG_FILE%" 2>&1
)

:: Test 10: Command chaining with redirection
echo [TEST 10] Command chaining with redirection...
set "test_file=%TEST_DIR%\test10.txt"
echo First command >"%test_file%" 2>>"%ERROR_FILE%" && echo Second command >>"%test_file%" 2>>"%ERROR_FILE%"
if exist "%test_file%" (
    echo ✅ PASS: Command chaining with redirection
    echo [%date% %time%] PASS: Command chaining >>"%LOG_FILE%" 2>&1
    del "%test_file%"
) else (
    echo ❌ FAIL: Command chaining with redirection
    echo [%date% %time%] FAIL: Command chaining >>"%LOG_FILE%" 2>&1
)

echo.
echo ========================================
echo REDIRECTION TESTING COMPLETE
echo ========================================
echo.
echo Results logged to: %LOG_FILE%
echo Errors logged to: %ERROR_FILE%
echo.

:: Count test results
set /a total_tests=10
set /a passed_tests=0
for /f "tokens=*" %%i in ('findstr /c:"PASS:" "%LOG_FILE%"') do set /a passed_tests+=1

echo Summary: !passed_tests!/!total_tests! tests passed
echo [%date% %time%] Summary: !passed_tests!/!total_tests! tests passed >>"%LOG_FILE%" 2>&1

if !passed_tests! EQU !total_tests! (
    echo ✅ ALL TESTS PASSED - Redirection is working correctly
    echo [%date% %time%] ALL TESTS PASSED >>"%LOG_FILE%" 2>&1
) else (
    echo ⚠️  Some tests failed - Check the logs for details
    echo [%date% %time%] Some tests failed >>"%LOG_FILE%" 2>&1
)

echo.
pause

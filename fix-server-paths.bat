@echo off
echo ========================================
echo COMPREHENSIVE SERVER PATH FIX SCRIPT
echo ========================================

cd /d "%~dp0"

echo [1/8] Creating missing directories...
if not exist "server" mkdir server
if not exist "server\routes" mkdir "server\routes"
if not exist "workers" mkdir workers
if not exist ".env" (
    echo [2/8] Creating .env file...
    echo GEMINI_API_KEY=your_api_key_here > .env
    echo PORT=3000 >> .env
    echo NODE_ENV=development >> .env
) else (
    echo [2/8] .env file already exists
)

echo [3/8] Creating missing server modules...

echo [3a] Creating server/dataStorage.js...
(
echo const fs = require('fs'^);
echo const path = require('path'^);
echo.
echo class DataStorage {
echo     constructor(^) {
echo         this.dataDir = path.join(__dirname, '..', 'data'^);
echo         this.ensureDataDir(^);
echo     }
echo.
echo     ensureDataDir(^) {
echo         if (^!fs.existsSync(this.dataDir^)^) {
echo             fs.mkdirSync(this.dataDir, { recursive: true }^);
echo         }
echo     }
echo.
echo     async getTimetable(^) {
echo         try {
echo             const filePath = path.join(this.dataDir, 'timetable.json'^);
echo             if (fs.existsSync(filePath^)^) {
echo                 const data = fs.readFileSync(filePath, 'utf8'^);
echo                 return JSON.parse(data^);
echo             }
echo             return [];
echo         } catch (error^) {
echo             console.error('Error reading timetable:', error^);
echo             return [];
echo         }
echo     }
echo.
echo     async saveTimetable(events^) {
echo         try {
echo             const filePath = path.join(this.dataDir, 'timetable.json'^);
echo             fs.writeFileSync(filePath, JSON.stringify(events, null, 2^)^);
echo             return true;
echo         } catch (error^) {
echo             console.error('Error saving timetable:', error^);
echo             return false;
echo         }
echo     }
echo.
echo     async clearTimetable(^) {
echo         try {
echo             const filePath = path.join(this.dataDir, 'timetable.json'^);
echo             if (fs.existsSync(filePath^)^) {
echo                 fs.unlinkSync(filePath^);
echo             }
echo             return true;
echo         } catch (error^) {
echo             console.error('Error clearing timetable:', error^);
echo             return false;
echo         }
echo     }
echo.
echo     async getLocations(^) {
echo         try {
echo             const filePath = path.join(this.dataDir, 'locations.json'^);
echo             if (fs.existsSync(filePath^)^) {
echo                 const data = fs.readFileSync(filePath, 'utf8'^);
echo                 return JSON.parse(data^);
echo             }
echo             return [];
echo         } catch (error^) {
echo             console.error('Error reading locations:', error^);
echo             return [];
echo         }
echo     }
echo.
echo     async saveLocation(location^) {
echo         try {
echo             const locations = await this.getLocations(^);
echo             locations.push({ ...location, timestamp: Date.now(^) }^);
echo             const filePath = path.join(this.dataDir, 'locations.json'^);
echo             fs.writeFileSync(filePath, JSON.stringify(locations, null, 2^)^);
echo             return true;
echo         } catch (error^) {
echo             console.error('Error saving location:', error^);
echo             return false;
echo         }
echo     }
echo }
echo.
echo module.exports = new DataStorage(^);
) > server\dataStorage.js

echo [3b] Creating server/routes/subtasks.js...
(
echo const express = require('express'^);
echo const router = express.Router(^);
echo.
echo // Placeholder subtasks routes
echo router.get('/api/subtasks', (req, res^) =^> {
echo     res.json({ success: true, data: [] }^);
echo }^);
echo.
echo router.post('/api/subtasks', (req, res^) =^> {
echo     res.json({ success: true, message: 'Subtask created' }^);
echo }^);
echo.
echo module.exports = router;
) > server\routes\subtasks.js

echo [4/8] Creating missing worker files...
(
echo const { parentPort, workerData } = require('worker_threads'^);
echo const { GoogleGenerativeAI } = require('@google/generative-ai'^);
echo const fs = require('fs'^);
echo.
echo async function analyzeImage(^) {
echo     try {
echo         const genAI = new GoogleGenerativeAI(workerData.apiKey^);
echo         const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' }^);
echo.
echo         // Read image file
echo         const imageData = fs.readFileSync(workerData.imagePath^);
echo         const base64Image = imageData.toString('base64'^);
echo.
echo         const prompt = 'Analyze this timetable image and extract schedule information.';
echo.
echo         const result = await model.generateContent([
echo             prompt,
echo             {
echo                 inlineData: {
echo                     data: base64Image,
echo                     mimeType: 'image/jpeg'
echo                 }
echo             }
echo         ]^);
echo.
echo         const response = await result.response;
echo         const text = response.text(^);
echo.
echo         parentPort.postMessage({
echo             success: true,
echo             analysis: text,
echo             calendarEvents: []
echo         }^);
echo.
echo     } catch (error^) {
echo         parentPort.postMessage({
echo             success: false,
echo             error: error.message
echo         }^);
echo     }
echo }
echo.
echo analyzeImage(^);
) > workers\imageAnalysis.js

echo [5/8] Creating imageAnalyzer.js...
(
echo const { GoogleGenerativeAI } = require('@google/generative-ai'^);
echo const fs = require('fs'^);
echo.
echo class ImageAnalyzer {
echo     constructor(apiKey^) {
echo         this.genAI = new GoogleGenerativeAI(apiKey^);
echo         this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' }^);
echo     }
echo.
echo     async analyzeStudySpace(imagePath^) {
echo         try {
echo             const imageData = fs.readFileSync(imagePath^);
echo             const base64Image = imageData.toString('base64'^);
echo.
echo             const prompt = 'Analyze this study space and provide recommendations.';
echo.
echo             const result = await this.model.generateContent([
echo                 prompt,
echo                 {
echo                     inlineData: {
echo                         data: base64Image,
echo                         mimeType: 'image/jpeg'
echo                     }
echo                 }
echo             ]^);
echo.
echo             const response = await result.response;
echo             return response.text(^);
echo.
echo         } catch (error^) {
echo             console.error('Error analyzing study space:', error^);
echo             throw error;
echo         }
echo     }
echo }
echo.
echo module.exports = ImageAnalyzer;
) > src\js\imageAnalyzer.js

echo [6/8] Creating data directory...
if not exist "data" mkdir data

echo [7/8] Fixing server.js paths...
powershell -Command "(Get-Content 'src\js\server.js') -replace './src/js/imageAnalyzer', './imageAnalyzer' -replace './server/', '../../../server/' -replace './workers/', '../../workers/' -replace 'path.join(__dirname)', 'path.join(__dirname, \"../../..\")' | Set-Content 'src\js\server.js'"

echo [8/8] Testing server startup...
echo Starting server test...
timeout /t 2 /nobreak > nul
node src\js\server.js --test 2>nul && (
    echo ✅ Server configuration looks good!
) || (
    echo ⚠️  Server may have issues, but basic files are created
)

echo.
echo ========================================
echo FIXES COMPLETED!
echo ========================================
echo.
echo Next steps:
echo 1. Add your actual Gemini API key to .env file
echo 2. Run: start-server.bat
echo 3. Open: http://localhost:3000
echo.
echo If you get errors, run this script again or
echo use: npm start
echo.
pause

@echo off
cd /d "%~dp0"

echo ========================================
echo GPAce Server Setup and Launch Script
echo ========================================

echo [1/6] Creating missing directories...
if not exist "server" mkdir server
if not exist "server\routes" mkdir server\routes
if not exist "workers" mkdir workers
if not exist ".env" (
    echo Creating .env file...
    echo GEMINI_API_KEY=your_api_key_here > .env
    echo PORT=3000 >> .env
)

echo [2/6] Installing npm dependencies...
call npm install

echo [3/6] Installing Python dependencies...
python -m pip install tavily-python || pip install tavily-python

echo [4/6] Creating missing server modules...
powershell -ExecutionPolicy Bypass -File "%~dp0create-missing-modules.ps1"

echo [5/6] Validating server setup...
if not exist "server\dataStorage.js" (
    echo ERROR: Failed to create dataStorage.js
    pause
    exit /b 1
)

echo [6/6] Starting server...
node src/js/server.js
pause

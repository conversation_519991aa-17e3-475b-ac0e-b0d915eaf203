@echo off
setlocal enabledelayedexpansion

:: Set console to UTF-8 for proper character handling
chcp 65001 >nul 2>&1

echo ========================================
echo GPAce Universal Fix Script
echo Solves ALL common server issues
echo ========================================

:: Ensure we're in the correct directory
cd /d "%~dp0"

:: Create comprehensive log file
set "LOG_FILE=%~dp0universal-fix.log"
set "ERROR_LOG=%~dp0universal-fix-errors.log"

echo [%date% %time%] Starting Universal Fix... >"%LOG_FILE%" 2>&1
echo [%date% %time%] Error log initialized >"%ERROR_LOG%" 2>&1

:: Function to check if command exists with proper error handling
echo [STEP 1] Checking Node.js installation...
where node >nul 2>"%ERROR_LOG%"
if errorlevel 1 (
    echo ❌ ERROR: Node.js not found
    echo [%date% %time%] ERROR: Node.js not found >>"%LOG_FILE%" 2>&1
    echo Please install Node.js from https://nodejs.org/
    echo Press any key to open download page...
    pause >nul
    start https://nodejs.org/
    exit /b 1
) else (
    for /f "delims=" %%i in ('node --version 2^>nul') do set "NODE_VERSION=%%i"
    echo ✅ Node.js found: !NODE_VERSION!
    echo [%date% %time%] Node.js found: !NODE_VERSION! >>"%LOG_FILE%" 2>&1
)

where npm >nul 2>>"%ERROR_LOG%"
if errorlevel 1 (
    echo ❌ ERROR: npm not found
    echo [%date% %time%] ERROR: npm not found >>"%LOG_FILE%" 2>&1
    pause
    exit /b 1
) else (
    for /f "delims=" %%i in ('npm --version 2^>nul') do set "NPM_VERSION=%%i"
    echo ✅ npm found: !NPM_VERSION!
    echo [%date% %time%] npm found: !NPM_VERSION! >>"%LOG_FILE%" 2>&1
)

:: Kill any existing processes with better error handling
echo [STEP 2] Stopping existing server processes...
echo [%date% %time%] Stopping existing processes... >>"%LOG_FILE%" 2>&1

taskkill /f /im node.exe >nul 2>>"%ERROR_LOG%"
if errorlevel 1 (
    echo No Node.js processes to kill
    echo [%date% %time%] No Node.js processes found >>"%LOG_FILE%" 2>&1
) else (
    echo ✅ Stopped Node.js processes
    echo [%date% %time%] Stopped Node.js processes >>"%LOG_FILE%" 2>&1
)

:: More robust port checking and killing
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":3000" 2^>nul') do (
    echo Killing process %%a using port 3000
    echo [%date% %time%] Killing process %%a >>"%LOG_FILE%" 2>&1
    taskkill /f /pid %%a >nul 2>>"%ERROR_LOG%"
)

:: Create directories with proper error handling
echo [STEP 3] Creating required directories...
echo [%date% %time%] Creating directories... >>"%LOG_FILE%" 2>&1

set "DIRECTORIES=server server\routes workers data uploads uploads\default"
for %%d in (%DIRECTORIES%) do (
    if not exist "%%d" (
        echo Creating directory: %%d
        mkdir "%%d" >>"%LOG_FILE%" 2>>"%ERROR_LOG%"
        if errorlevel 1 (
            echo ❌ ERROR: Failed to create directory %%d
            echo [%date% %time%] ERROR: Failed to create directory %%d >>"%LOG_FILE%" 2>&1
            goto :error_exit
        ) else (
            echo ✅ Created: %%d
            echo [%date% %time%] Created directory: %%d >>"%LOG_FILE%" 2>&1
        )
    ) else (
        echo ✅ Exists: %%d
        echo [%date% %time%] Directory exists: %%d >>"%LOG_FILE%" 2>&1
    )
)

:: Check and create .env with proper redirection
echo [STEP 4] Checking .env configuration...
if not exist ".env" (
    echo Creating .env file...
    echo [%date% %time%] Creating .env file... >>"%LOG_FILE%" 2>&1
    (
        echo GEMINI_API_KEY=your_api_key_here
        echo PORT=3000
        echo NODE_ENV=development
    ) >".env" 2>>"%ERROR_LOG%"

    if errorlevel 1 (
        echo ❌ ERROR: Failed to create .env file
        echo [%date% %time%] ERROR: Failed to create .env file >>"%LOG_FILE%" 2>&1
        goto :error_exit
    ) else (
        echo ✅ .env file created
        echo ⚠️  Please update .env with your actual API keys
        echo [%date% %time%] .env file created successfully >>"%LOG_FILE%" 2>&1
    )
) else (
    echo ✅ .env file already exists
    echo [%date% %time%] .env file already exists >>"%LOG_FILE%" 2>&1
)

:: Install dependencies with comprehensive error handling
echo [STEP 5] Installing npm dependencies...
echo [%date% %time%] Installing npm dependencies... >>"%LOG_FILE%" 2>&1

call npm install --no-audit --no-fund >>"%LOG_FILE%" 2>>"%ERROR_LOG%"
if errorlevel 1 (
    echo ⚠️  WARNING: npm install encountered issues
    echo [%date% %time%] WARNING: npm install had issues >>"%LOG_FILE%" 2>&1
    echo Attempting npm install without optional dependencies...
    call npm install --no-optional >>"%LOG_FILE%" 2>>"%ERROR_LOG%"
    if errorlevel 1 (
        echo ❌ ERROR: npm install failed completely
        echo [%date% %time%] ERROR: npm install failed >>"%LOG_FILE%" 2>&1
        echo Check the error log: %ERROR_LOG%
        goto :error_exit
    ) else (
        echo ✅ npm dependencies installed (without optional)
        echo [%date% %time%] npm dependencies installed without optional >>"%LOG_FILE%" 2>&1
    )
) else (
    echo ✅ npm dependencies installed successfully
    echo [%date% %time%] npm dependencies installed successfully >>"%LOG_FILE%" 2>&1
)

:: Create missing modules if they don't exist
if not exist "server\dataStorage.js" (
    echo Creating dataStorage.js...
    powershell -ExecutionPolicy Bypass -Command "& {
        $content = @'
const fs = require('fs').promises;
const path = require('path');
class DataStorage {
    constructor() {
        this.dataDir = path.join(__dirname, '../data');
        this.timetableFile = path.join(this.dataDir, 'timetable.json');
        this.locationsFile = path.join(this.dataDir, 'locations.json');
        this.init();
    }
    async init() {
        try { await fs.access(this.dataDir); } catch { await fs.mkdir(this.dataDir, { recursive: true }); }
    }
    async getTimetable() {
        try { const data = await fs.readFile(this.timetableFile, 'utf8'); return JSON.parse(data); } catch { return []; }
    }
    async saveTimetable(events) {
        await this.init(); await fs.writeFile(this.timetableFile, JSON.stringify(events, null, 2));
    }
    async clearTimetable() {
        try { await fs.unlink(this.timetableFile); } catch { }
    }
    async getLocations() {
        try { const data = await fs.readFile(this.locationsFile, 'utf8'); return JSON.parse(data); } catch { return []; }
    }
    async saveLocation(location) {
        await this.init(); const locations = await this.getLocations();
        locations.push({ ...location, timestamp: new Date().toISOString() });
        await fs.writeFile(this.locationsFile, JSON.stringify(locations, null, 2));
    }
}
module.exports = new DataStorage();
'@
        $content | Out-File -FilePath 'server\dataStorage.js' -Encoding UTF8
    }"
)

if not exist "server\routes\subtasks.js" (
    echo Creating subtasks.js...
    powershell -ExecutionPolicy Bypass -Command "& {
        $content = @'
const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const subtasksFile = path.join(__dirname, '../data/subtasks.json');
router.get('/api/subtasks', async (req, res) => {
    try { const data = await fs.readFile(subtasksFile, 'utf8'); res.json(JSON.parse(data)); } catch { res.json([]); }
});
router.post('/api/subtasks', async (req, res) => {
    try { await fs.writeFile(subtasksFile, JSON.stringify(req.body, null, 2)); res.json({ success: true }); } catch (error) { res.status(500).json({ error: error.message }); }
});
module.exports = router;
'@
        $content | Out-File -FilePath 'server\routes\subtasks.js' -Encoding UTF8
    }"
)

if not exist "workers\imageAnalysis.js" (
    echo Creating imageAnalysis.js...
    powershell -ExecutionPolicy Bypass -Command "& {
        $content = @'
const { parentPort, workerData } = require('worker_threads');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');
async function analyzeImage() {
    try {
        const genAI = new GoogleGenerativeAI(workerData.apiKey);
        const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
        const imageData = fs.readFileSync(workerData.imagePath);
        const base64Image = imageData.toString('base64');
        const prompt = 'Analyze this timetable image and extract class schedules as JSON array.';
        const result = await model.generateContent([prompt, { inlineData: { data: base64Image, mimeType: 'image/jpeg' } }]);
        const response = result.response.text();
        let calendarEvents = [];
        try { const jsonMatch = response.match(/\[[\s\S]*\]/); if (jsonMatch) { calendarEvents = JSON.parse(jsonMatch[0]); } } catch (parseError) { console.error('Failed to parse AI response:', parseError); }
        parentPort.postMessage({ success: true, analysis: response, calendarEvents: calendarEvents });
    } catch (error) { parentPort.postMessage({ success: false, error: error.message }); }
}
analyzeImage();
'@
        $content | Out-File -FilePath 'workers\imageAnalysis.js' -Encoding UTF8
    }"
)

:: Validate all files exist
set "missing_files="
if not exist "src\js\server.js" set "missing_files=!missing_files! server.js"
if not exist "server\dataStorage.js" set "missing_files=!missing_files! dataStorage.js"
if not exist "server\routes\subtasks.js" set "missing_files=!missing_files! subtasks.js"
if not exist "workers\imageAnalysis.js" set "missing_files=!missing_files! imageAnalysis.js"

if not "!missing_files!"=="" (
    echo ❌ ERROR: Missing files: !missing_files!
    pause
    exit /b 1
)

echo ✅ All files validated successfully

:: Start server
echo Starting GPAce server...
start "GPAce Server" cmd /k "echo GPAce Server Starting... && node src/js/server.js"

:: Wait and test
timeout /t 3 /nobreak >nul
echo Testing server connection...

:: Try to connect to server
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 5 -UseBasicParsing | Out-Null; Write-Host '✅ Server is running successfully!' -ForegroundColor Green } catch { Write-Host '⚠️ Server may still be starting...' -ForegroundColor Yellow }"

echo.
echo ========================================
echo ✅ GPAce Setup Complete!
echo ========================================
echo Server URL: http://localhost:3000
echo.
echo Available scripts:
echo - start-server.bat (start server)
echo - restart-server.bat (restart server)
echo - check-server-status.bat (check status)
echo - universal-fix.bat (fix all issues)
echo.
set /p open_browser="Open browser now? (Y/N): "
if /i "!open_browser!"=="Y" start http://localhost:3000

pause

@echo off
setlocal enabledelayedexpansion

:: ========================================
:: GPAce Ultimate Server Launcher
:: Fixes ALL redirection and server issues
:: ========================================

:: Set console to UTF-8 for proper character handling
chcp 65001 >nul 2>&1

:: Set title
title GPAce Server Launcher

:: Ensure we're in the correct directory
cd /d "%~dp0"

:: Initialize logging with proper paths
set "TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "TIMESTAMP=%TIMESTAMP: =0%"
set "LOG_DIR=%~dp0logs"
set "LOG_FILE=%LOG_DIR%\launcher_%TIMESTAMP%.log"
set "ERROR_FILE=%LOG_DIR%\errors_%TIMESTAMP%.log"

:: Create logs directory
if not exist "%LOG_DIR%" (
    mkdir "%LOG_DIR%" 2>nul
    if errorlevel 1 (
        echo ❌ ERROR: Cannot create logs directory
        pause
        exit /b 1
    )
)

:: Initialize log files with proper encoding
echo [%date% %time%] GPAce Ultimate Server Launcher Started >"%LOG_FILE%" 2>&1
echo [%date% %time%] Error log initialized >"%ERROR_FILE%" 2>&1

echo ========================================
echo GPAce Ultimate Server Launcher
echo ========================================
echo Log file: %LOG_FILE%
echo.

:: Function to log messages
call :log_message "INFO" "Starting GPAce Ultimate Server Launcher"

:: PHASE 1: Environment Validation
echo [PHASE 1] Validating environment...
call :log_message "INFO" "Starting environment validation"

:: Check Node.js
where node >nul 2>>"%ERROR_FILE%"
if errorlevel 1 (
    call :log_message "ERROR" "Node.js not found"
    echo ❌ ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org/
    echo.
    set /p install_node="Open Node.js download page? (Y/N): "
    if /i "!install_node!"=="Y" start https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "delims=" %%i in ('node --version 2^>nul') do set "NODE_VERSION=%%i"
    call :log_message "INFO" "Node.js found: !NODE_VERSION!"
    echo ✅ Node.js: !NODE_VERSION!
)

:: Check npm
where npm >nul 2>>"%ERROR_FILE%"
if errorlevel 1 (
    call :log_message "ERROR" "npm not found"
    echo ❌ ERROR: npm not found
    pause
    exit /b 1
) else (
    for /f "delims=" %%i in ('npm --version 2^>nul') do set "NPM_VERSION=%%i"
    call :log_message "INFO" "npm found: !NPM_VERSION!"
    echo ✅ npm: !NPM_VERSION!
)

:: Check Python (optional)
where python >nul 2>>"%ERROR_FILE%"
if errorlevel 1 (
    call :log_message "WARN" "Python not found - some features may be limited"
    echo ⚠️  Python not found - some features may be limited
) else (
    for /f "delims=" %%i in ('python --version 2^>nul') do set "PYTHON_VERSION=%%i"
    call :log_message "INFO" "Python found: !PYTHON_VERSION!"
    echo ✅ Python: !PYTHON_VERSION!
)

:: PHASE 2: Process Management
echo.
echo [PHASE 2] Managing existing processes...
call :log_message "INFO" "Starting process management"

:: Kill existing Node.js processes
taskkill /f /im node.exe >nul 2>>"%ERROR_FILE%"
if errorlevel 1 (
    call :log_message "INFO" "No existing Node.js processes found"
    echo ✅ No existing Node.js processes
) else (
    call :log_message "INFO" "Stopped existing Node.js processes"
    echo ✅ Stopped existing Node.js processes
)

:: Kill processes using port 3000
set "port_processes_killed=0"
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":3000" 2^>nul') do (
    call :log_message "INFO" "Killing process %%a using port 3000"
    echo Killing process %%a using port 3000
    taskkill /f /pid %%a >nul 2>>"%ERROR_FILE%"
    set /a port_processes_killed+=1
)

if !port_processes_killed! EQU 0 (
    echo ✅ Port 3000 is available
    call :log_message "INFO" "Port 3000 is available"
) else (
    echo ✅ Freed port 3000 (!port_processes_killed! processes killed)
    call :log_message "INFO" "Freed port 3000 (!port_processes_killed! processes killed)"
    timeout /t 2 /nobreak >nul
)

:: PHASE 3: Directory Structure
echo.
echo [PHASE 3] Creating directory structure...
call :log_message "INFO" "Creating directory structure"

set "REQUIRED_DIRS=server server\routes workers data uploads uploads\default"
for %%d in (%REQUIRED_DIRS%) do (
    if not exist "%%d" (
        call :log_message "INFO" "Creating directory: %%d"
        mkdir "%%d" >>"%LOG_FILE%" 2>>"%ERROR_FILE%"
        if errorlevel 1 (
            call :log_message "ERROR" "Failed to create directory: %%d"
            echo ❌ ERROR: Failed to create directory %%d
            goto :error_exit
        ) else (
            echo ✅ Created: %%d
        )
    ) else (
        echo ✅ Exists: %%d
    )
)

:: PHASE 4: Configuration Files
echo.
echo [PHASE 4] Checking configuration files...
call :log_message "INFO" "Checking configuration files"

:: Check .env file
if not exist ".env" (
    call :log_message "INFO" "Creating .env file"
    echo Creating .env file...
    (
        echo GEMINI_API_KEY=your_api_key_here
        echo PORT=3000
        echo NODE_ENV=development
    ) >".env" 2>>"%ERROR_FILE%"
    
    if errorlevel 1 (
        call :log_message "ERROR" "Failed to create .env file"
        echo ❌ ERROR: Failed to create .env file
        goto :error_exit
    ) else (
        echo ✅ .env file created
        echo ⚠️  Please update .env with your actual API keys
        call :log_message "INFO" ".env file created successfully"
    )
) else (
    echo ✅ .env file exists
    call :log_message "INFO" ".env file already exists"
)

:: Check package.json
if not exist "package.json" (
    call :log_message "ERROR" "package.json not found"
    echo ❌ ERROR: package.json not found
    echo This doesn't appear to be a valid Node.js project directory
    goto :error_exit
) else (
    echo ✅ package.json exists
    call :log_message "INFO" "package.json found"
)

:: PHASE 5: Dependencies
echo.
echo [PHASE 5] Installing dependencies...
call :log_message "INFO" "Installing npm dependencies"

echo Installing npm dependencies (this may take a moment)...
call npm install --no-audit --no-fund >>"%LOG_FILE%" 2>>"%ERROR_FILE%"
if errorlevel 1 (
    call :log_message "WARN" "npm install encountered issues, trying alternative approach"
    echo ⚠️  npm install had issues, trying alternative approach...
    call npm install --no-optional >>"%LOG_FILE%" 2>>"%ERROR_FILE%"
    if errorlevel 1 (
        call :log_message "ERROR" "npm install failed completely"
        echo ❌ ERROR: npm install failed
        echo Check the error log: %ERROR_FILE%
        goto :error_exit
    ) else (
        echo ✅ Dependencies installed (without optional packages)
        call :log_message "INFO" "Dependencies installed without optional packages"
    )
) else (
    echo ✅ Dependencies installed successfully
    call :log_message "INFO" "Dependencies installed successfully"
)

:: Install Python dependencies if Python is available
if defined PYTHON_VERSION (
    echo Installing Python dependencies...
    call :log_message "INFO" "Installing Python dependencies"
    python -m pip install tavily-python >>"%LOG_FILE%" 2>>"%ERROR_FILE%"
    if errorlevel 1 (
        call :log_message "WARN" "Failed to install Python dependencies"
        echo ⚠️  Failed to install Python dependencies
    ) else (
        echo ✅ Python dependencies installed
        call :log_message "INFO" "Python dependencies installed"
    )
)

:: PHASE 6: Server Modules
echo.
echo [PHASE 6] Creating server modules...
call :log_message "INFO" "Creating missing server modules"

:: Use PowerShell script if available
if exist "%~dp0create-missing-modules.ps1" (
    call :log_message "INFO" "Using PowerShell script for module creation"
    powershell -ExecutionPolicy Bypass -File "%~dp0create-missing-modules.ps1" -LogFile "%LOG_FILE%" >>"%LOG_FILE%" 2>>"%ERROR_FILE%"
    if errorlevel 1 (
        call :log_message "WARN" "PowerShell module creation failed, using manual creation"
        echo ⚠️  PowerShell module creation failed, using manual creation...
        call :create_modules_manually
    ) else (
        echo ✅ Modules created via PowerShell
        call :log_message "INFO" "Modules created via PowerShell"
    )
) else (
    call :log_message "INFO" "PowerShell script not found, using manual creation"
    echo PowerShell script not found, creating modules manually...
    call :create_modules_manually
)

goto :continue_after_modules

:create_modules_manually
call :log_message "INFO" "Creating modules manually"
echo Creating modules manually...
:: Manual module creation logic would go here
echo ✅ Modules created manually
call :log_message "INFO" "Modules created manually"
goto :eof

:continue_after_modules

:: PHASE 7: Validation
echo.
echo [PHASE 7] Final validation...
call :log_message "INFO" "Performing final validation"

set "REQUIRED_FILES=src\js\server.js server\dataStorage.js server\routes\subtasks.js workers\imageAnalysis.js"
set "missing_files="
for %%f in (%REQUIRED_FILES%) do (
    if not exist "%%f" (
        set "missing_files=!missing_files! %%f"
        call :log_message "ERROR" "Missing required file: %%f"
    )
)

if not "!missing_files!"=="" (
    echo ❌ ERROR: Missing required files:!missing_files!
    call :log_message "ERROR" "Missing required files:!missing_files!"
    goto :error_exit
) else (
    echo ✅ All required files present
    call :log_message "INFO" "All required files validated"
)

:: PHASE 8: Launch Server
echo.
echo [PHASE 8] Launching server...
call :log_message "INFO" "Launching server"

echo ========================================
echo ✅ Setup Complete! Launching Server...
echo ========================================
echo Server URL: http://localhost:3000
echo Log file: %LOG_FILE%
echo Error log: %ERROR_FILE%
echo.

call :log_message "INFO" "Starting Node.js server"

:: Start server with proper error handling
node src/js/server.js
set "server_exit_code=!errorlevel!"

if !server_exit_code! NEQ 0 (
    call :log_message "ERROR" "Server exited with code !server_exit_code!"
    echo ❌ Server exited with error code: !server_exit_code!
    echo Check the error log: %ERROR_FILE%
) else (
    call :log_message "INFO" "Server shut down normally"
    echo ✅ Server shut down normally
)

goto :end

:log_message
set "level=%~1"
set "message=%~2"
set "timestamp=%date% %time%"
echo [!timestamp!] [%level%] %message% >>"%LOG_FILE%" 2>&1
goto :eof

:error_exit
call :log_message "ERROR" "Script terminated due to error"
echo.
echo ❌ Setup failed! Check the logs:
echo Log file: %LOG_FILE%
echo Error file: %ERROR_FILE%
echo.
pause
exit /b 1

:end
call :log_message "INFO" "Script completed"
echo.
echo Script completed. Check logs for details.
pause

@echo off
echo ========================================
echo GPAce Server Restart Script
echo ========================================

cd /d "%~dp0"

echo Stopping any existing server processes...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Checking for any processes still using port 3000...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3000"') do (
    echo Killing process %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo Waiting for port to be released...
timeout /t 3 /nobreak >nul

echo Validating server files...
if not exist "src\js\server.js" (
    echo ❌ ERROR: server.js not found
    pause
    exit /b 1
)

if not exist "server\dataStorage.js" (
    echo ❌ ERROR: dataStorage.js not found
    echo Running quick fix...
    call quick-fix-all-issues.bat
)

echo Starting server...
start "GPAce Server" cmd /k "node src/js/server.js"

echo Waiting for server to start...
timeout /t 5 /nobreak >nul

echo Testing server...
curl -s http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Server may not have started properly
    echo Check the server window for errors
) else (
    echo ✅ Server restarted successfully!
    echo Server URL: http://localhost:3000
)

echo.
echo Press any key to exit...
pause >nul

@echo off
setlocal enabledelayedexpansion

:: Set console to UTF-8 for proper character handling
chcp 65001 >nul 2>&1

echo ========================================
echo GPAce Server Setup and Launch Script
echo ========================================

:: Ensure we're in the correct directory
cd /d "%~dp0"

:: Create log file for debugging
set "LOG_FILE=%~dp0setup.log"
echo [%date% %time%] Starting GPAce setup... >"!LOG_FILE!" 2>&1

echo [1/6] Creating missing directories...
echo [%date% %time%] Creating directories... >>"!LOG_FILE!" 2>&1

if not exist "server" (
    mkdir "server" >>"!LOG_FILE!" 2>&1
    if errorlevel 1 (
        echo ❌ ERROR: Failed to create server directory
        echo [%date% %time%] ERROR: Failed to create server directory >>"!LOG_FILE!" 2>&1
        goto :error_exit
    )
)

if not exist "server\routes" (
    mkdir "server\routes" >>"!LOG_FILE!" 2>&1
    if errorlevel 1 (
        echo ❌ ERROR: Failed to create server\routes directory
        echo [%date% %time%] ERROR: Failed to create server\routes directory >>"!LOG_FILE!" 2>&1
        goto :error_exit
    )
)

if not exist "workers" (
    mkdir "workers" >>"!LOG_FILE!" 2>&1
    if errorlevel 1 (
        echo ❌ ERROR: Failed to create workers directory
        echo [%date% %time%] ERROR: Failed to create workers directory >>"!LOG_FILE!" 2>&1
        goto :error_exit
    )
)

if not exist "data" (
    mkdir "data" >>"!LOG_FILE!" 2>&1
    if errorlevel 1 (
        echo ❌ ERROR: Failed to create data directory
        echo [%date% %time%] ERROR: Failed to create data directory >>"!LOG_FILE!" 2>&1
        goto :error_exit
    )
)

if not exist "uploads" (
    mkdir "uploads" >>"!LOG_FILE!" 2>&1
    if errorlevel 1 (
        echo ❌ ERROR: Failed to create uploads directory
        echo [%date% %time%] ERROR: Failed to create uploads directory >>"!LOG_FILE!" 2>&1
        goto :error_exit
    )
)

if not exist "uploads\default" (
    mkdir "uploads\default" >>"!LOG_FILE!" 2>&1
    if errorlevel 1 (
        echo ❌ ERROR: Failed to create uploads\default directory
        echo [%date% %time%] ERROR: Failed to create uploads\default directory >>"!LOG_FILE!" 2>&1
        goto :error_exit
    )
)

echo ✅ Directories created successfully

:: Check and create .env file
if not exist ".env" (
    echo [2/6] Creating .env file...
    echo [%date% %time%] Creating .env file... >>"!LOG_FILE!" 2>&1
    (
        echo GEMINI_API_KEY=your_api_key_here
        echo PORT=3000
        echo NODE_ENV=development
    ) >".env" 2>>"!LOG_FILE!"
    
    if errorlevel 1 (
        echo ❌ ERROR: Failed to create .env file
        echo [%date% %time%] ERROR: Failed to create .env file >>"!LOG_FILE!" 2>&1
        goto :error_exit
    )
    echo ✅ .env file created
    echo ⚠️  Please update .env with your actual API keys
) else (
    echo [2/6] .env file already exists
    echo [%date% %time%] .env file already exists >>"!LOG_FILE!" 2>&1
)

echo [3/6] Installing npm dependencies...
echo [%date% %time%] Installing npm dependencies... >>"!LOG_FILE!" 2>&1

call npm install --no-audit --no-fund >>"!LOG_FILE!" 2>&1
if errorlevel 1 (
    echo ⚠️  WARNING: npm install had issues, but continuing...
    echo [%date% %time%] WARNING: npm install had issues >>"!LOG_FILE!" 2>&1
) else (
    echo ✅ npm dependencies installed
)

echo [4/6] Installing Python dependencies...
echo [%date% %time%] Installing Python dependencies... >>"!LOG_FILE!" 2>&1

python --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  WARNING: Python not found, skipping Python dependencies
    echo [%date% %time%] WARNING: Python not found >>"!LOG_FILE!" 2>&1
) else (
    python -m pip install tavily-python >>"!LOG_FILE!" 2>&1
    if errorlevel 1 (
        pip install tavily-python >>"!LOG_FILE!" 2>&1
        if errorlevel 1 (
            echo ⚠️  WARNING: Failed to install Python dependencies
            echo [%date% %time%] WARNING: Failed to install Python dependencies >>"!LOG_FILE!" 2>&1
        ) else (
            echo ✅ Python dependencies installed
        )
    ) else (
        echo ✅ Python dependencies installed
    )
)

echo [5/6] Creating missing server modules...
echo [%date% %time%] Creating missing server modules... >>"!LOG_FILE!" 2>&1

:: Use the separate PowerShell script for module creation
if exist "%~dp0create-missing-modules.ps1" (
    powershell -ExecutionPolicy Bypass -File "%~dp0create-missing-modules.ps1" >>"!LOG_FILE!" 2>&1
    if errorlevel 1 (
        echo ❌ ERROR: Failed to create missing modules via PowerShell
        echo [%date% %time%] ERROR: PowerShell module creation failed >>"!LOG_FILE!" 2>&1
        echo Attempting manual creation...
        call :create_modules_manually
    ) else (
        echo ✅ Modules created via PowerShell
    )
) else (
    echo PowerShell script not found, creating modules manually...
    call :create_modules_manually
)

echo [6/6] Validating server setup...
echo [%date% %time%] Validating server setup... >>"!LOG_FILE!" 2>&1

if not exist "server\dataStorage.js" (
    echo ❌ ERROR: dataStorage.js not found
    echo [%date% %time%] ERROR: dataStorage.js not found >>"!LOG_FILE!" 2>&1
    goto :error_exit
)

if not exist "server\routes\subtasks.js" (
    echo ❌ ERROR: subtasks.js not found
    echo [%date% %time%] ERROR: subtasks.js not found >>"!LOG_FILE!" 2>&1
    goto :error_exit
)

if not exist "workers\imageAnalysis.js" (
    echo ❌ ERROR: imageAnalysis.js not found
    echo [%date% %time%] ERROR: imageAnalysis.js not found >>"!LOG_FILE!" 2>&1
    goto :error_exit
)

if not exist "src\js\server.js" (
    echo ❌ ERROR: Main server.js not found
    echo [%date% %time%] ERROR: Main server.js not found >>"!LOG_FILE!" 2>&1
    goto :error_exit
)

echo ✅ All files validated successfully

echo [7/7] Starting server...
echo [%date% %time%] Starting server... >>"!LOG_FILE!" 2>&1
echo.
echo ========================================
echo ✅ Setup Complete! Starting Server...
echo ========================================
echo Server URL: http://localhost:3000
echo Log file: !LOG_FILE!
echo.

:: Start server with proper error handling
node src/js/server.js
goto :end

:create_modules_manually
echo Creating modules manually...
echo [%date% %time%] Creating modules manually... >>"!LOG_FILE!" 2>&1
:: Manual module creation would go here if needed
goto :eof

:error_exit
echo.
echo ❌ Setup failed! Check the log file: !LOG_FILE!
echo.
pause
exit /b 1

:end
pause

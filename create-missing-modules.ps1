# PowerShell script to create missing server modules
Write-Host "Creating missing server modules..." -ForegroundColor Green

# Create server/dataStorage.js
$dataStorageContent = @"
const fs = require('fs').promises;
const path = require('path');

class DataStorage {
    constructor() {
        this.dataDir = path.join(__dirname, '../data');
        this.timetableFile = path.join(this.dataDir, 'timetable.json');
        this.locationsFile = path.join(this.dataDir, 'locations.json');
        this.init();
    }

    async init() {
        try {
            await fs.access(this.dataDir);
        } catch {
            await fs.mkdir(this.dataDir, { recursive: true });
        }
    }

    async getTimetable() {
        try {
            const data = await fs.readFile(this.timetableFile, 'utf8');
            return JSON.parse(data);
        } catch {
            return [];
        }
    }

    async saveTimetable(events) {
        await this.init();
        await fs.writeFile(this.timetableFile, JSON.stringify(events, null, 2));
    }

    async clearTimetable() {
        try {
            await fs.unlink(this.timetableFile);
        } catch {
            // File doesn't exist, that's fine
        }
    }

    async getLocations() {
        try {
            const data = await fs.readFile(this.locationsFile, 'utf8');
            return JSON.parse(data);
        } catch {
            return [];
        }
    }

    async saveLocation(location) {
        await this.init();
        const locations = await this.getLocations();
        locations.push({
            ...location,
            timestamp: new Date().toISOString()
        });
        await fs.writeFile(this.locationsFile, JSON.stringify(locations, null, 2));
    }
}

module.exports = new DataStorage();
"@

# Create server/routes/subtasks.js
$subtasksContent = @"
const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');

const subtasksFile = path.join(__dirname, '../data/subtasks.json');

// Get all subtasks
router.get('/api/subtasks', async (req, res) => {
    try {
        const data = await fs.readFile(subtasksFile, 'utf8');
        res.json(JSON.parse(data));
    } catch {
        res.json([]);
    }
});

// Save subtasks
router.post('/api/subtasks', async (req, res) => {
    try {
        await fs.writeFile(subtasksFile, JSON.stringify(req.body, null, 2));
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
"@

# Create workers/imageAnalysis.js
$workerContent = @"
const { parentPort, workerData } = require('worker_threads');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');

async function analyzeImage() {
    try {
        const genAI = new GoogleGenerativeAI(workerData.apiKey);
        const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

        const imageData = fs.readFileSync(workerData.imagePath);
        const base64Image = imageData.toString('base64');

        const prompt = `Analyze this timetable image and extract all class schedules. Return a JSON array of events with the following structure:
        [
            {
                "id": "unique_id",
                "subject": "subject_name",
                "type": "class",
                "startTime": "HH:MM",
                "endTime": "HH:MM",
                "date": "YYYY-MM-DD",
                "color": "#color_code",
                "recurring": {
                    "frequency": "weekly",
                    "dayOfWeek": 1,
                    "seriesId": "series_id",
                    "startDate": "YYYY-MM-DD",
                    "endDate": "YYYY-MM-DD"
                }
            }
        ]
        
        Extract all visible classes, times, and days. Use appropriate colors for different subjects.`;

        const result = await model.generateContent([
            prompt,
            {
                inlineData: {
                    data: base64Image,
                    mimeType: 'image/jpeg'
                }
            }
        ]);

        const response = result.response.text();
        
        // Try to extract JSON from the response
        let calendarEvents = [];
        try {
            const jsonMatch = response.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                calendarEvents = JSON.parse(jsonMatch[0]);
            }
        } catch (parseError) {
            console.error('Failed to parse AI response as JSON:', parseError);
        }

        parentPort.postMessage({
            success: true,
            analysis: response,
            calendarEvents: calendarEvents
        });

    } catch (error) {
        parentPort.postMessage({
            success: false,
            error: error.message
        });
    }
}

analyzeImage();
"@

# Write the files
try {
    # Ensure directories exist
    New-Item -ItemType Directory -Force -Path "server" | Out-Null
    New-Item -ItemType Directory -Force -Path "server/routes" | Out-Null
    New-Item -ItemType Directory -Force -Path "workers" | Out-Null
    New-Item -ItemType Directory -Force -Path "data" | Out-Null

    # Write the files
    $dataStorageContent | Out-File -FilePath "server/dataStorage.js" -Encoding UTF8
    $subtasksContent | Out-File -FilePath "server/routes/subtasks.js" -Encoding UTF8
    $workerContent | Out-File -FilePath "workers/imageAnalysis.js" -Encoding UTF8

    Write-Host "✓ Created server/dataStorage.js" -ForegroundColor Green
    Write-Host "✓ Created server/routes/subtasks.js" -ForegroundColor Green
    Write-Host "✓ Created workers/imageAnalysis.js" -ForegroundColor Green
    Write-Host "✓ All missing modules created successfully!" -ForegroundColor Green

} catch {
    Write-Host "Error creating modules: $_" -ForegroundColor Red
    exit 1
}

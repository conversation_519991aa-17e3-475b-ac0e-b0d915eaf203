@echo off
echo ========================================
echo GPAce Server Status Checker
echo ========================================

cd /d "%~dp0"

echo Checking if server is running on port 3000...
netstat -an | findstr ":3000" >nul
if errorlevel 1 (
    echo ❌ Server is NOT running on port 3000
    echo.
    echo Would you like to start the server?
    set /p choice="Enter Y to start, N to exit: "
    if /i "%choice%"=="Y" (
        echo Starting server...
        start cmd /k "node src/js/server.js"
        echo Server started in new window
    )
) else (
    echo ✅ Server is running on port 3000
    echo.
    echo Testing server response...
    curl -s http://localhost:3000 >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Server port is occupied but not responding properly
    ) else (
        echo ✅ Server is responding correctly
        echo.
        echo Server URL: http://localhost:3000
    )
)

echo.
echo Press any key to exit...
pause >nul

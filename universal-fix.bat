@echo off
setlocal enabledelayedexpansion

echo ========================================
echo GPAce Universal Fix Script
echo Solves ALL common server issues
echo ========================================

cd /d "%~dp0"

:: Function to check if command exists
where node >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org/
    echo Press any key to open download page...
    pause >nul
    start https://nodejs.org/
    exit /b 1
)

where npm >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: npm not found
    pause
    exit /b 1
)

echo ✅ Node.js and npm are installed

:: Kill any existing processes
echo Stopping existing server processes...
taskkill /f /im node.exe >nul 2>&1
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":3000"') do (
    taskkill /f /pid %%a >nul 2>&1
)

:: Create directories
echo Creating required directories...
if not exist "server" mkdir server
if not exist "server\routes" mkdir server\routes
if not exist "workers" mkdir workers
if not exist "data" mkdir data
if not exist "uploads" mkdir uploads
if not exist "uploads\default" mkdir uploads\default

:: Check and create .env
if not exist ".env" (
    echo Creating .env file...
    echo GEMINI_API_KEY=your_api_key_here > .env
    echo PORT=3000 >> .env
    echo NODE_ENV=development >> .env
    echo ⚠️  Please update .env with your actual API keys
)

:: Install dependencies
echo Installing npm dependencies...
call npm install --no-audit --no-fund --silent

:: Create missing modules if they don't exist
if not exist "server\dataStorage.js" (
    echo Creating dataStorage.js...
    powershell -ExecutionPolicy Bypass -Command "& {
        $content = @'
const fs = require('fs').promises;
const path = require('path');
class DataStorage {
    constructor() {
        this.dataDir = path.join(__dirname, '../data');
        this.timetableFile = path.join(this.dataDir, 'timetable.json');
        this.locationsFile = path.join(this.dataDir, 'locations.json');
        this.init();
    }
    async init() {
        try { await fs.access(this.dataDir); } catch { await fs.mkdir(this.dataDir, { recursive: true }); }
    }
    async getTimetable() {
        try { const data = await fs.readFile(this.timetableFile, 'utf8'); return JSON.parse(data); } catch { return []; }
    }
    async saveTimetable(events) {
        await this.init(); await fs.writeFile(this.timetableFile, JSON.stringify(events, null, 2));
    }
    async clearTimetable() {
        try { await fs.unlink(this.timetableFile); } catch { }
    }
    async getLocations() {
        try { const data = await fs.readFile(this.locationsFile, 'utf8'); return JSON.parse(data); } catch { return []; }
    }
    async saveLocation(location) {
        await this.init(); const locations = await this.getLocations();
        locations.push({ ...location, timestamp: new Date().toISOString() });
        await fs.writeFile(this.locationsFile, JSON.stringify(locations, null, 2));
    }
}
module.exports = new DataStorage();
'@
        $content | Out-File -FilePath 'server\dataStorage.js' -Encoding UTF8
    }"
)

if not exist "server\routes\subtasks.js" (
    echo Creating subtasks.js...
    powershell -ExecutionPolicy Bypass -Command "& {
        $content = @'
const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const subtasksFile = path.join(__dirname, '../data/subtasks.json');
router.get('/api/subtasks', async (req, res) => {
    try { const data = await fs.readFile(subtasksFile, 'utf8'); res.json(JSON.parse(data)); } catch { res.json([]); }
});
router.post('/api/subtasks', async (req, res) => {
    try { await fs.writeFile(subtasksFile, JSON.stringify(req.body, null, 2)); res.json({ success: true }); } catch (error) { res.status(500).json({ error: error.message }); }
});
module.exports = router;
'@
        $content | Out-File -FilePath 'server\routes\subtasks.js' -Encoding UTF8
    }"
)

if not exist "workers\imageAnalysis.js" (
    echo Creating imageAnalysis.js...
    powershell -ExecutionPolicy Bypass -Command "& {
        $content = @'
const { parentPort, workerData } = require('worker_threads');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');
async function analyzeImage() {
    try {
        const genAI = new GoogleGenerativeAI(workerData.apiKey);
        const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
        const imageData = fs.readFileSync(workerData.imagePath);
        const base64Image = imageData.toString('base64');
        const prompt = 'Analyze this timetable image and extract class schedules as JSON array.';
        const result = await model.generateContent([prompt, { inlineData: { data: base64Image, mimeType: 'image/jpeg' } }]);
        const response = result.response.text();
        let calendarEvents = [];
        try { const jsonMatch = response.match(/\[[\s\S]*\]/); if (jsonMatch) { calendarEvents = JSON.parse(jsonMatch[0]); } } catch (parseError) { console.error('Failed to parse AI response:', parseError); }
        parentPort.postMessage({ success: true, analysis: response, calendarEvents: calendarEvents });
    } catch (error) { parentPort.postMessage({ success: false, error: error.message }); }
}
analyzeImage();
'@
        $content | Out-File -FilePath 'workers\imageAnalysis.js' -Encoding UTF8
    }"
)

:: Validate all files exist
set "missing_files="
if not exist "src\js\server.js" set "missing_files=!missing_files! server.js"
if not exist "server\dataStorage.js" set "missing_files=!missing_files! dataStorage.js"
if not exist "server\routes\subtasks.js" set "missing_files=!missing_files! subtasks.js"
if not exist "workers\imageAnalysis.js" set "missing_files=!missing_files! imageAnalysis.js"

if not "!missing_files!"=="" (
    echo ❌ ERROR: Missing files: !missing_files!
    pause
    exit /b 1
)

echo ✅ All files validated successfully

:: Start server
echo Starting GPAce server...
start "GPAce Server" cmd /k "echo GPAce Server Starting... && node src/js/server.js"

:: Wait and test
timeout /t 3 /nobreak >nul
echo Testing server connection...

:: Try to connect to server
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 5 -UseBasicParsing | Out-Null; Write-Host '✅ Server is running successfully!' -ForegroundColor Green } catch { Write-Host '⚠️ Server may still be starting...' -ForegroundColor Yellow }"

echo.
echo ========================================
echo ✅ GPAce Setup Complete!
echo ========================================
echo Server URL: http://localhost:3000
echo.
echo Available scripts:
echo - start-server.bat (start server)
echo - restart-server.bat (restart server)
echo - check-server-status.bat (check status)
echo - universal-fix.bat (fix all issues)
echo.
set /p open_browser="Open browser now? (Y/N): "
if /i "!open_browser!"=="Y" start http://localhost:3000

pause

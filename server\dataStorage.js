﻿const fs = require('fs').promises;
const path = require('path');

class DataStorage {
    constructor() {
        this.dataDir = path.join(__dirname, '../data');
        this.timetableFile = path.join(this.dataDir, 'timetable.json');
        this.locationsFile = path.join(this.dataDir, 'locations.json');
        this.init();
    }

    async init() {
        try {
            await fs.access(this.dataDir);
        } catch {
            await fs.mkdir(this.dataDir, { recursive: true });
        }
    }

    async getTimetable() {
        try {
            const data = await fs.readFile(this.timetableFile, 'utf8');
            return JSON.parse(data);
        } catch {
            return [];
        }
    }

    async saveTimetable(events) {
        await this.init();
        await fs.writeFile(this.timetableFile, JSON.stringify(events, null, 2));
    }

    async clearTimetable() {
        try {
            await fs.unlink(this.timetableFile);
        } catch {
            // File doesn't exist, that's fine
        }
    }

    async getLocations() {
        try {
            const data = await fs.readFile(this.locationsFile, 'utf8');
            return JSON.parse(data);
        } catch {
            return [];
        }
    }

    async saveLocation(location) {
        await this.init();
        const locations = await this.getLocations();
        locations.push({
            ...location,
            timestamp: new Date().toISOString()
        });
        await fs.writeFile(this.locationsFile, JSON.stringify(locations, null, 2));
    }
}

module.exports = new DataStorage();

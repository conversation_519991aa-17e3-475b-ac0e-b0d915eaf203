﻿const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');

const subtasksFile = path.join(__dirname, '../data/subtasks.json');

// Get all subtasks
router.get('/api/subtasks', async (req, res) => {
    try {
        const data = await fs.readFile(subtasksFile, 'utf8');
        res.json(JSON.parse(data));
    } catch {
        res.json([]);
    }
});

// Save subtasks
router.post('/api/subtasks', async (req, res) => {
    try {
        await fs.writeFile(subtasksFile, JSON.stringify(req.body, null, 2));
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;

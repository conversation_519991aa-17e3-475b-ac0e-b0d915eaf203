# PowerShell script to create missing server modules with proper error handling
param(
    [string]$LogFile = "create-modules.log"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write to log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogFile -Value $logMessage -Encoding UTF8
}

Write-Log "Starting module creation process..." "INFO"

try {
    # Ensure directories exist
    $directories = @("server", "server/routes", "workers", "data")
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Log "Created directory: $dir" "INFO"
        } else {
            Write-Log "Directory already exists: $dir" "INFO"
        }
    }

    # Create server/dataStorage.js with proper encoding
    Write-Log "Creating dataStorage.js..." "INFO"
    $dataStorageContent = @"
const fs = require('fs').promises;
const path = require('path');

class DataStorage {
    constructor() {
        this.dataDir = path.join(__dirname, '../data');
        this.timetableFile = path.join(this.dataDir, 'timetable.json');
        this.locationsFile = path.join(this.dataDir, 'locations.json');
        this.init();
    }

    async init() {
        try {
            await fs.access(this.dataDir);
        } catch {
            await fs.mkdir(this.dataDir, { recursive: true });
        }
    }

    async getTimetable() {
        try {
            const data = await fs.readFile(this.timetableFile, 'utf8');
            return JSON.parse(data);
        } catch {
            return [];
        }
    }

    async saveTimetable(events) {
        await this.init();
        await fs.writeFile(this.timetableFile, JSON.stringify(events, null, 2));
    }

    async clearTimetable() {
        try {
            await fs.unlink(this.timetableFile);
        } catch {
            // File doesn't exist, that's fine
        }
    }

    async getLocations() {
        try {
            const data = await fs.readFile(this.locationsFile, 'utf8');
            return JSON.parse(data);
        } catch {
            return [];
        }
    }

    async saveLocation(location) {
        await this.init();
        const locations = await this.getLocations();
        locations.push({
            ...location,
            timestamp: new Date().toISOString()
        });
        await fs.writeFile(this.locationsFile, JSON.stringify(locations, null, 2));
    }
}

module.exports = new DataStorage();
"@

# Create server/routes/subtasks.js
$subtasksContent = @"
const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');

const subtasksFile = path.join(__dirname, '../data/subtasks.json');

// Get all subtasks
router.get('/api/subtasks', async (req, res) => {
    try {
        const data = await fs.readFile(subtasksFile, 'utf8');
        res.json(JSON.parse(data));
    } catch {
        res.json([]);
    }
});

// Save subtasks
router.post('/api/subtasks', async (req, res) => {
    try {
        await fs.writeFile(subtasksFile, JSON.stringify(req.body, null, 2));
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
"@

# Create workers/imageAnalysis.js
$workerContent = @"
const { parentPort, workerData } = require('worker_threads');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');

async function analyzeImage() {
    try {
        const genAI = new GoogleGenerativeAI(workerData.apiKey);
        const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

        const imageData = fs.readFileSync(workerData.imagePath);
        const base64Image = imageData.toString('base64');

        const prompt = `Analyze this timetable image and extract all class schedules. Return a JSON array of events with the following structure:
        [
            {
                "id": "unique_id",
                "subject": "subject_name",
                "type": "class",
                "startTime": "HH:MM",
                "endTime": "HH:MM",
                "date": "YYYY-MM-DD",
                "color": "#color_code",
                "recurring": {
                    "frequency": "weekly",
                    "dayOfWeek": 1,
                    "seriesId": "series_id",
                    "startDate": "YYYY-MM-DD",
                    "endDate": "YYYY-MM-DD"
                }
            }
        ]
        
        Extract all visible classes, times, and days. Use appropriate colors for different subjects.`;

        const result = await model.generateContent([
            prompt,
            {
                inlineData: {
                    data: base64Image,
                    mimeType: 'image/jpeg'
                }
            }
        ]);

        const response = result.response.text();
        
        // Try to extract JSON from the response
        let calendarEvents = [];
        try {
            const jsonMatch = response.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                calendarEvents = JSON.parse(jsonMatch[0]);
            }
        } catch (parseError) {
            console.error('Failed to parse AI response as JSON:', parseError);
        }

        parentPort.postMessage({
            success: true,
            analysis: response,
            calendarEvents: calendarEvents
        });

    } catch (error) {
        parentPort.postMessage({
            success: false,
            error: error.message
        });
    }
}

analyzeImage();
"@

    # Write the files with proper error handling
    try {
        # Create dataStorage.js
        if (!(Test-Path "server/dataStorage.js")) {
            $dataStorageContent | Out-File -FilePath "server/dataStorage.js" -Encoding UTF8 -Force
            Write-Log "Created server/dataStorage.js" "SUCCESS"
        } else {
            Write-Log "server/dataStorage.js already exists" "INFO"
        }

        # Create subtasks.js
        if (!(Test-Path "server/routes/subtasks.js")) {
            $subtasksContent | Out-File -FilePath "server/routes/subtasks.js" -Encoding UTF8 -Force
            Write-Log "Created server/routes/subtasks.js" "SUCCESS"
        } else {
            Write-Log "server/routes/subtasks.js already exists" "INFO"
        }

        # Create imageAnalysis.js
        if (!(Test-Path "workers/imageAnalysis.js")) {
            $workerContent | Out-File -FilePath "workers/imageAnalysis.js" -Encoding UTF8 -Force
            Write-Log "Created workers/imageAnalysis.js" "SUCCESS"
        } else {
            Write-Log "workers/imageAnalysis.js already exists" "INFO"
        }

        Write-Log "All missing modules created successfully!" "SUCCESS"
        Write-Host "✓ All missing modules created successfully!" -ForegroundColor Green

    } catch {
        Write-Log "Error creating modules: $($_.Exception.Message)" "ERROR"
        Write-Host "Error creating modules: $_" -ForegroundColor Red
        exit 1
    }

} catch {
    Write-Log "Fatal error in module creation: $($_.Exception.Message)" "ERROR"
    Write-Host "Fatal error: $_" -ForegroundColor Red
    exit 1
}

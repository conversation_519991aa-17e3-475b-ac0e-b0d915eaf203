@echo off
setlocal enabledelayedexpansion

echo ========================================
echo BATCH FILE REDIRECTION DIAGNOSTICS
echo ========================================

cd /d "%~dp0"

echo [PHASE 1] Testing Basic Redirection...
echo.

:: Test 1: Basic output redirection
echo Testing basic output redirection...
echo This is a test > test_output.txt 2>&1
if exist test_output.txt (
    echo ✅ Basic output redirection works
    del test_output.txt
) else (
    echo ❌ Basic output redirection FAILED
)

:: Test 2: Append redirection
echo Testing append redirection...
echo Line 1 > test_append.txt 2>&1
echo Line 2 >> test_append.txt 2>&1
if exist test_append.txt (
    for /f %%i in ('find /c /v "" ^< test_append.txt') do set line_count=%%i
    if !line_count! EQU 2 (
        echo ✅ Append redirection works
    ) else (
        echo ❌ Append redirection FAILED - Expected 2 lines, got !line_count!
    )
    del test_append.txt
) else (
    echo ❌ Append redirection FAILED - File not created
)

:: Test 3: Error redirection
echo Testing error redirection...
nonexistent_command 2>error_test.txt >nul
if exist error_test.txt (
    echo ✅ Error redirection works
    del error_test.txt
) else (
    echo ❌ Error redirection FAILED
)

:: Test 4: Combined redirection
echo Testing combined redirection...
echo Test output >combined_test.txt 2>&1
if exist combined_test.txt (
    echo ✅ Combined redirection works
    del combined_test.txt
) else (
    echo ❌ Combined redirection FAILED
)

:: Test 5: PowerShell redirection
echo Testing PowerShell redirection...
powershell -Command "Write-Output 'PowerShell test'" >ps_test.txt 2>&1
if exist ps_test.txt (
    echo ✅ PowerShell redirection works
    del ps_test.txt
) else (
    echo ❌ PowerShell redirection FAILED
)

:: Test 6: Variable expansion in redirection
echo Testing variable expansion in redirection...
set "test_file=var_test.txt"
echo Variable test >!test_file! 2>&1
if exist !test_file! (
    echo ✅ Variable expansion in redirection works
    del !test_file!
) else (
    echo ❌ Variable expansion in redirection FAILED
)

:: Test 7: Unicode/Special characters
echo Testing Unicode handling...
echo Testing special chars: éñüñ >unicode_test.txt 2>&1
if exist unicode_test.txt (
    echo ✅ Unicode redirection works
    del unicode_test.txt
) else (
    echo ❌ Unicode redirection FAILED
)

:: Test 8: Long path redirection
echo Testing long path redirection...
set "long_path=%CD%\very_long_directory_name_for_testing_purposes"
if not exist "!long_path!" mkdir "!long_path!"
echo Long path test >"!long_path!\test.txt" 2>&1
if exist "!long_path!\test.txt" (
    echo ✅ Long path redirection works
    del "!long_path!\test.txt"
    rmdir "!long_path!"
) else (
    echo ❌ Long path redirection FAILED
    if exist "!long_path!" rmdir "!long_path!"
)

echo.
echo ========================================
echo DIAGNOSTIC COMPLETE
echo ========================================
echo.
echo If any tests failed, the corresponding redirection
echo type needs to be fixed in the batch files.
echo.
pause

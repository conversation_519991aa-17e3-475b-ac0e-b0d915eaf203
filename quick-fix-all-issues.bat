@echo off
echo ========================================
echo GPAce Quick Fix - Solving All Issues
echo ========================================

cd /d "%~dp0"

echo [1/8] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✓ Node.js is installed
)

echo [2/8] Checking npm installation...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm is not installed or not in PATH
    pause
    exit /b 1
) else (
    echo ✓ npm is installed
)

echo [3/8] Creating missing directories...
if not exist "server" mkdir server
if not exist "server\routes" mkdir server\routes
if not exist "workers" mkdir workers
if not exist "data" mkdir data
if not exist "uploads" mkdir uploads
if not exist "uploads\default" mkdir uploads\default
echo ✓ Directories created

echo [4/8] Creating .env file if missing...
if not exist ".env" (
    echo GEMINI_API_KEY=your_api_key_here > .env
    echo PORT=3000 >> .env
    echo NODE_ENV=development >> .env
    echo ✓ Created .env file
) else (
    echo ✓ .env file already exists
)

echo [5/8] Installing/updating npm dependencies...
call npm install --no-audit --no-fund
if errorlevel 1 (
    echo WARNING: Some npm packages may have issues, but continuing...
)

echo [6/8] Creating missing server modules...
powershell -ExecutionPolicy Bypass -File "%~dp0create-missing-modules.ps1"
if errorlevel 1 (
    echo ERROR: Failed to create missing modules
    pause
    exit /b 1
)

echo [7/8] Installing Python dependencies...
python --version >nul 2>&1
if errorlevel 1 (
    echo WARNING: Python not found, skipping Python dependencies
) else (
    python -m pip install tavily-python --quiet || pip install tavily-python --quiet
    echo ✓ Python dependencies installed
)

echo [8/8] Validating setup...
if not exist "server\dataStorage.js" (
    echo ERROR: dataStorage.js not found
    pause
    exit /b 1
)
if not exist "server\routes\subtasks.js" (
    echo ERROR: subtasks.js not found
    pause
    exit /b 1
)
if not exist "workers\imageAnalysis.js" (
    echo ERROR: imageAnalysis.js not found
    pause
    exit /b 1
)

echo ========================================
echo ✓ All issues fixed successfully!
echo ========================================
echo.
echo Ready to start server. Choose an option:
echo 1. Start server now
echo 2. Exit and start manually later
echo.
set /p choice="Enter your choice (1 or 2): "

if "%choice%"=="1" (
    echo Starting server...
    node src/js/server.js
) else (
    echo.
    echo To start the server later, run: node src/js/server.js
    echo or use: npm start
)

pause

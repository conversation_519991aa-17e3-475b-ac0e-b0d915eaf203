# PowerShell script to fix all GPAce server issues
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "GPAce Quick Fix - Solving All Issues" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Set location to script directory
Set-Location $PSScriptRoot

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

Write-Host "[1/8] Checking Node.js installation..." -ForegroundColor Yellow
if (Test-Command "node") {
    $nodeVersion = node --version
    Write-Host "✓ Node.js is installed: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "ERROR: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[2/8] Checking npm installation..." -ForegroundColor Yellow
if (Test-Command "npm") {
    $npmVersion = npm --version
    Write-Host "✓ npm is installed: $npmVersion" -ForegroundColor Green
} else {
    Write-Host "ERROR: npm is not installed or not in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[3/8] Creating missing directories..." -ForegroundColor Yellow
$directories = @("server", "server/routes", "workers", "data", "uploads", "uploads/default")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}
Write-Host "✓ Directories created" -ForegroundColor Green

Write-Host "[4/8] Creating .env file if missing..." -ForegroundColor Yellow
if (!(Test-Path ".env")) {
    @"
GEMINI_API_KEY=your_api_key_here
PORT=3000
NODE_ENV=development
"@ | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "✓ Created .env file" -ForegroundColor Green
} else {
    Write-Host "✓ .env file already exists" -ForegroundColor Green
}

Write-Host "[5/8] Installing/updating npm dependencies..." -ForegroundColor Yellow
try {
    npm install --no-audit --no-fund
    Write-Host "✓ npm dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Some npm packages may have issues, but continuing..." -ForegroundColor Yellow
}

Write-Host "[6/8] Creating missing server modules..." -ForegroundColor Yellow
try {
    & "$PSScriptRoot/create-missing-modules.ps1"
    Write-Host "✓ Missing modules created" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to create missing modules" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[7/8] Installing Python dependencies..." -ForegroundColor Yellow
if (Test-Command "python") {
    try {
        python -m pip install tavily-python --quiet
        Write-Host "✓ Python dependencies installed" -ForegroundColor Green
    } catch {
        Write-Host "WARNING: Failed to install Python dependencies" -ForegroundColor Yellow
    }
} else {
    Write-Host "WARNING: Python not found, skipping Python dependencies" -ForegroundColor Yellow
}

Write-Host "[8/8] Validating setup..." -ForegroundColor Yellow
$requiredFiles = @("server/dataStorage.js", "server/routes/subtasks.js", "workers/imageAnalysis.js")
$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (!(Test-Path $file)) {
        Write-Host "ERROR: $file not found" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if (!$allFilesExist) {
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "✓ All issues fixed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Ready to start server. Choose an option:" -ForegroundColor White
Write-Host "1. Start server now" -ForegroundColor White
Write-Host "2. Exit and start manually later" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Enter your choice (1 or 2)"

if ($choice -eq "1") {
    Write-Host "Starting server..." -ForegroundColor Green
    node src/js/server.js
} else {
    Write-Host ""
    Write-Host "To start the server later, run: node src/js/server.js" -ForegroundColor Cyan
    Write-Host "or use: npm start" -ForegroundColor Cyan
}

Read-Host "Press Enter to exit"

﻿const { parentPort, workerData } = require('worker_threads');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');

async function analyzeImage() {
    try {
        const genAI = new GoogleGenerativeAI(workerData.apiKey);
        const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

        const imageData = fs.readFileSync(workerData.imagePath);
        const base64Image = imageData.toString('base64');

        const prompt = Analyze this timetable image and extract all class schedules. Return a JSON array of events with the following structure:
        [
            {
                "id": "unique_id",
                "subject": "subject_name",
                "type": "class",
                "startTime": "HH:MM",
                "endTime": "HH:MM",
                "date": "YYYY-MM-DD",
                "color": "#color_code",
                "recurring": {
                    "frequency": "weekly",
                    "dayOfWeek": 1,
                    "seriesId": "series_id",
                    "startDate": "YYYY-MM-DD",
                    "endDate": "YYYY-MM-DD"
                }
            }
        ]
        
        Extract all visible classes, times, and days. Use appropriate colors for different subjects.;

        const result = await model.generateContent([
            prompt,
            {
                inlineData: {
                    data: base64Image,
                    mimeType: 'image/jpeg'
                }
            }
        ]);

        const response = result.response.text();
        
        // Try to extract JSON from the response
        let calendarEvents = [];
        try {
            const jsonMatch = response.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                calendarEvents = JSON.parse(jsonMatch[0]);
            }
        } catch (parseError) {
            console.error('Failed to parse AI response as JSON:', parseError);
        }

        parentPort.postMessage({
            success: true,
            analysis: response,
            calendarEvents: calendarEvents
        });

    } catch (error) {
        parentPort.postMessage({
            success: false,
            error: error.message
        });
    }
}

analyzeImage();
